# Generated values.dev54.yaml for 54dev environment
# Based on merged configs from tbd/api, tbd/api-mobile, tbd/admin

replicaCount: 1
namespace: 54dev

image:
  repository: sdbp-php
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets:
  - name: "default-secret"

podAnnotations: {}
podSecurityContext: {}
securityContext: {}

service:
  type: ClusterIP
  port: 9000

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 50m
    memory: 64Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80



admin:
  paymentOwnerSystemTitle: "ООО «ПодКонтролем»"
  telegram:
    failSend: true
    token: ""
    failChatId: ""
  lkp:
    endpoint: localhost:5000
    enabled: false
    logLevel: 2
    ssl: true
    updateStatus: 60000
  regionId: "0"
  mapProvider: yandex
  language: ru

# Keycloak configuration
keycloak:
  baseUrl: http://keycloak:8080
  enabled: false
  clientId: ""
  clientSecret: ""
  realm: ""

# Logging configuration
logging:
  success: true
  error: true
  methods: POST,GET,PUT,DELETE
  answer: true
  server: true
  get: true
  post: true
  files: true
  sql: true

# MySQL configuration (from merged configs)
mysql:
  host: db54dev-node1.tkp2.prod
  port: 3306
  database: sdbp54kubeprod
  username: gds-user
  password: ""
  socket: ""
  debug: true

# Redis configuration
redis:
  host: ************
  port: 6379
  password: ""

# PHP-FPM configuration
fpm:
  maxChildren: 50
  startServers: 5
  minSpareServers: 5
  maxSpareServers: 35
  maxRequests: 500
  requestTerminateTimeout: 300
  requestSlowlogTimeout: 10
  phpAdminValues:
    memory_limit: 256M
    max_execution_time: 300
    upload_max_filesize: 64M
    post_max_size: 64M

# Cache configuration
cache:
  class: File
  duration: +1 hours
  prefix: sdbp_54dev
  path: cache/
  mask: 0777

# Security configuration
security:
  loginLenMin: 3
  loginLenMax: 255
  loginTriesMax: 3
  loginTriesLock: 120
  pswLenMin: 6
  pswLenMax: 255
  rememberMeTime: 2592000
  pswLenMinCashRegister: 8
  pswLenMaxCashRegister: 12

# Directory configuration
directories:
  cashRegisterCfg: "../config/cash_register/"

# City configuration
city:
  excludeCitiesOnNameFromList: "[]"
  showAllCitiesOnYMap: false

# Date configuration
date:
  format: "Y-m-d"
  pickerFormat: "yyyy-mm-dd"

api:
  gdsHost: 'api.sdbp.54dev-kube.tkp2.prod/'

# Instance configuration
instance:
  prefix: "054dev:sdbp:1"

useSecretTemplate: false

# Cron jobs configuration for 54dev
cronJobs:
  - name: load-terminal-data
    schedule: "*/8 * * * *"
    args:
      - ./api/cli/api_action.php
      - --action=load_all_terminal_data
      - --partner_id=1
      - --http_host=api.sdbp.54dev-kube.tkp2.prod
      - --forks=1

  - name: parse-transactions
    schedule: "*/1 * * * *"
    args:
      - ./api-mobile/cli/cron_parse_transactions.php
      - --http_host=api-mobile.sdbp.54dev-kube.tkp2.prod

  - name: bill-emv-transaction
    schedule: "*/1 * * * *"
    args:
      - ./api/cli/api_action.php
      - --action=bill_emv_transaction
      - --max_count=5000
      - --partner_id=1
      - --http_host=api.sdbp.54dev-kube.tkp2.prod
      - --forks=1
      - --ordered=1

  - name: check-emv-abonements
    schedule: "*/1 * * * *"
    args:
      - ./api/cli/api_action.php
      - --action=check_emv_abonements
      - --max_count=5000
      - --partner_id=1
      - --http_host=api.sdbp.54dev-kube.tkp2.prod
      - --forks=1

  - name: recalculate-stoplist
    schedule: "*/20 * * * *"
    args:
      - ./admin/cli/cron_recalculate_stoplist.php
      - --http_host=admin.sdbp.54dev-kube.tkp2.prod
      - --time_limit=19 minutes

  - name: emission-import
    schedule: "*/10 * * * *"
    args:
      - ./admin/cli/cron_emission_import.php
      - --http_host=admin.sdbp.54dev-kube.tkp2.prod

  - name: reload-city
    schedule: "*/21 * * * *"
    args:
      - ./api/cli/api_action.php
      - --action=reload_city
      - --partner_id=1
      - --http_host=api.sdbp.54dev-kube.tkp2.prod
      - --forks=1

  - name: ride-all-short
    schedule: "*/5 * * * *"
    args:
      - ./api/cli/api_action.php
      - --action=ride_all
      - --partner_id=1
      - --http_host=api.sdbp.54dev-kube.tkp2.prod
      - --date_end_shift=0
      - --forks=1

  - name: ride-all-long
    schedule: "*/30 * * * *"
    args:
      - ./api/cli/api_action.php
      - --action=ride_all
      - --partner_id=1
      - --http_host=api.sdbp.54dev-kube.tkp2.prod
      - --date_end_shift=2
      - --forks=1

  - name: waypoints
    schedule: "*/15 * * * *"
    args:
      - ./api/cli/api_action.php
      - --action=waypoints
      - --partner_id=1
      - --http_host=api.sdbp.54dev-kube.tkp2.prod
      - --forks=1
